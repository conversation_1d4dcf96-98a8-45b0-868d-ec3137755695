import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Plus,
  Trash2,
  Edit3,
  LogOut,
  User,
  MessageSquare,
  ShoppingCart,
} from "lucide-react";
import type { ChatHistory } from "../../types/chat";
import type { AuthUserData } from "../../services/auth";
import chatService from "../../services/chatService";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  chats: ChatHistory[];
  currentChatId: string | null;
  onChatSelect: (chatId: string) => void;
  onNewChat: () => void;
  user: AuthUserData | null;
  onLogout: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  onClose,
  chats,
  currentChatId,
  onChatSelect,
  onNewChat,
  user,
  onLogout,
}) => {
  const [editingChatId, setEditingChatId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState("");

  const handleEditTitle = async (chatId: string, newTitle: string) => {
    try {
      await chatService.updateChatTitle(chatId, newTitle);
      setEditingChatId(null);
      setEditingTitle("");
    } catch (error) {
      console.error("Error updating chat title:", error);
    }
  };

  const handleDeleteChat = async (chatId: string) => {
    if (window.confirm("Are you sure you want to delete this chat?")) {
      try {
        await chatService.deleteChat(chatId);
      } catch (error) {
        console.error("Error deleting chat:", error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return "Today";
    } else if (diffDays === 2) {
      return "Yesterday";
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <>
      {/* Mobile overlay backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={onClose}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: "-100%" }}
            animate={{ x: 0 }}
            exit={{ x: "-100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="fixed left-0 top-0 h-full w-80 bg-gray-900 text-white z-50 lg:relative lg:translate-x-0 lg:w-64"
          >
            <div className="flex flex-col h-full">
              <div className="p-3 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ShoppingCart className="w-5 h-5 text-white" />
                    <h2 className="text-sm font-medium text-white">
                      Shopping Assistant
                    </h2>
                  </div>
                  <button
                    onClick={onClose}
                    className="lg:hidden p-1 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <X className="w-4 h-4 text-gray-300" />
                  </button>
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onNewChat}
                  className="w-full mt-3 bg-transparent border border-gray-600 text-white py-2 px-3 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center space-x-2 text-sm"
                >
                  <Plus className="w-4 h-4" />
                  <span>New chat</span>
                </motion.button>
              </div>

              <div className="flex-1 overflow-y-auto px-2 py-2">
                <AnimatePresence>
                  {chats.map((chat) => (
                    <motion.div
                      key={chat.chat_id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className={`mb-1 rounded-lg transition-colors group ${
                        currentChatId === chat.chat_id
                          ? "bg-gray-700"
                          : "hover:bg-gray-700"
                      }`}
                    >
                      <div className="px-3 py-2">
                        {editingChatId === chat.chat_id ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={editingTitle}
                              onChange={(e) => setEditingTitle(e.target.value)}
                              onKeyPress={(e) => {
                                if (e.key === "Enter") {
                                  handleEditTitle(chat.chat_id, editingTitle);
                                }
                              }}
                              onBlur={() => {
                                setEditingChatId(null);
                                setEditingTitle("");
                              }}
                              className="flex-1 px-2 py-1 text-sm bg-gray-600 text-white border border-gray-500 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                              autoFocus
                            />
                          </div>
                        ) : (
                          <div className="flex items-center justify-between">
                            <button
                              onClick={() => onChatSelect(chat.chat_id)}
                              className="flex-1 text-left truncate"
                            >
                              <div className="text-sm text-gray-200 truncate">
                                {chat.title}
                              </div>
                            </button>

                            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <button
                                onClick={() => {
                                  setEditingChatId(chat.chat_id);
                                  setEditingTitle(chat.title);
                                }}
                                className="p-1 rounded hover:bg-gray-600 transition-colors"
                                title="Edit title"
                              >
                                <Edit3 className="w-3 h-3 text-gray-400" />
                              </button>
                              <button
                                onClick={() => handleDeleteChat(chat.chat_id)}
                                className="p-1 rounded hover:bg-gray-600 transition-colors"
                                title="Delete chat"
                              >
                                <Trash2 className="w-3 h-3 text-gray-400" />
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>

                {chats.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    <MessageSquare className="w-12 h-12 mx-auto mb-3 text-gray-600" />
                    <p>No chats yet</p>
                    <p className="text-sm">Start a new conversation!</p>
                  </div>
                )}
              </div>

              <div className="p-3 border-t border-gray-700">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-7 h-7 bg-gray-700 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-gray-300" />
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-200">
                      {user?.username}
                    </div>
                    <div className="text-xs text-gray-400">{user?.email}</div>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onLogout}
                  className="w-full bg-transparent border border-gray-600 text-gray-300 py-2 px-3 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center space-x-2 text-sm"
                >
                  <LogOut className="w-4 h-4" />
                  <span>Logout</span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Sidebar;
