import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  Plus,
  Trash2,
  Edit3,
  LogOut,
  ShoppingCart,
  User,
  MessageSquare,
  Loader2,
} from "lucide-react";
import { useAuth } from "../../hooks/useAuth";
import chatService from "../../services/chatService";
import type { Message, ChatHistory } from "../../types/chat";
import type { AuthUserData } from "../../services/auth";
import ChatMessage from "./ChatMessage";
import Sidebar from "./Sidebar";

const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [chats, setChats] = useState<ChatHistory[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (user) {
      loadUserChats();
    }
  }, [user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadUserChats = async () => {
    if (!user) return;
    try {
      const userChats = await chatService.getUserChats(user.id);
      setChats(userChats);
    } catch (error) {
      console.error("Error loading chats:", error);
    }
  };

  const loadChatHistory = async (chatId: string) => {
    try {
      const history = await chatService.getChatHistory(chatId);
      const formattedMessages: Message[] = history.map((msg) => ({
        role: msg.role as "user" | "assistant",
        content: msg.content,
      }));
      setMessages(formattedMessages);
      setCurrentChatId(chatId);
    } catch (error) {
      console.error("Error loading chat history:", error);
    }
  };

  const createNewChat = () => {
    setMessages([]);
    setCurrentChatId(null);
    setSidebarOpen(false);
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || loading) return;

    const userMessage: Message = {
      role: "user",
      content: inputMessage.trim(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");
    setLoading(true);

    try {
      const request = {
        messages: [...messages, userMessage],
        chat_id: currentChatId || undefined,
      };

      const response = await chatService.sendMessage(request);

      const assistantMessage: Message = {
        role: "assistant",
        content: response.response,
      };

      setMessages((prev) => [...prev, assistantMessage]);

      if (!currentChatId) {
        setCurrentChatId(response.chat_id);
        await loadUserChats(); // Refresh chat list
      }
    } catch (error) {
      console.error("Error sending message:", error);
      // Remove the user message if there was an error
      setMessages((prev) => prev.slice(0, -1));
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="h-screen bg-gray-50 flex">
      {/* Desktop Sidebar - Always visible on desktop */}
      <div className="hidden lg:block">
        <Sidebar
          isOpen={true}
          onClose={() => {}}
          chats={chats}
          currentChatId={currentChatId}
          onChatSelect={loadChatHistory}
          onNewChat={createNewChat}
          user={user}
          onLogout={handleLogout}
        />
      </div>

      {/* Mobile Sidebar - Overlay */}
      <div className="lg:hidden">
        <Sidebar
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          chats={chats}
          currentChatId={currentChatId}
          onChatSelect={loadChatHistory}
          onNewChat={createNewChat}
          user={user}
          onLogout={handleLogout}
        />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col bg-white">
        {/* Header - Mobile only */}
        <header className="lg:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <MessageSquare className="w-5 h-5 text-gray-600" />
            </button>
            <div className="flex items-center space-x-2">
              <ShoppingCart className="w-6 h-6 text-primary-600" />
              <h1 className="text-xl font-semibold text-gray-900">
                Shopping Assistant
              </h1>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <User className="w-4 h-4" />
              <span>{user?.username}</span>
            </div>
            <button
              onClick={handleLogout}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              title="Logout"
            >
              <LogOut className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </header>

        {/* Chat Container - A4 width centered */}
        <div className="flex-1 flex justify-center">
          <div className="w-full max-w-4xl flex flex-col">
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto px-4 py-6">
              <div className="max-w-3xl mx-auto space-y-6">
                <AnimatePresence>
                  {messages.map((message, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChatMessage message={message} />
                    </motion.div>
                  ))}
                </AnimatePresence>

                {loading && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex items-center space-x-2 text-gray-500"
                  >
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Assistant is typing...</span>
                  </motion.div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Input Area */}
            <div className="border-t border-gray-200 px-4 py-4">
              <div className="max-w-3xl mx-auto">
                <div className="flex items-end space-x-3">
                  <div className="flex-1">
                    <textarea
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Ask me anything about shopping..."
                      className="w-full p-4 border border-gray-300 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent shadow-sm"
                      rows={1}
                      style={{ minHeight: "52px", maxHeight: "120px" }}
                    />
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleSendMessage}
                    disabled={!inputMessage.trim() || loading}
                    className="bg-primary-600 text-white p-3 rounded-xl hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm"
                  >
                    <Send className="w-5 h-5" />
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
