// Version: 1.0.1 - Message export fix
import api from "./api";

export interface Message {
  role: "user" | "assistant";
  content: string;
}

export interface ChatRequest {
  messages: Message[];
  chat_id?: string;
}

export interface ChatResponse {
  chat_id: string;
  response: string;
  status: string;
}

export interface ChatHistory {
  chat_id: string;
  title: string;
  created_at: string;
  updated_at: string;
}

export interface MessageResponse {
  message_id: string;
  role: string;
  content: string;
  timestamp: string;
}

class ChatService {
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await api.post("/chat", request);
    return response.data;
  }

  async getChatHistory(chatId: string): Promise<MessageResponse[]> {
    const response = await api.get(`/chat/${chatId}/history`);
    return response.data;
  }

  async getUserChats(userId: string): Promise<ChatHistory[]> {
    const response = await api.get(`/chat/${userId}/user-chats`);
    return response.data;
  }

  async getChatDetails(chatId: string): Promise<ChatHistory> {
    const response = await api.get(`/chat/${chatId}`);
    return response.data;
  }

  async updateChatTitle(chatId: string, title: string): Promise<ChatHistory> {
    const response = await api.put(`/chat/${chatId}/title`, { title });
    return response.data;
  }

  async deleteChat(chatId: string): Promise<{ message: string }> {
    const response = await api.delete(`/chat/${chatId}`);
    return response.data;
  }

  async getChatModels(): Promise<any[]> {
    const response = await api.get("/chat/models");
    return response.data;
  }
}

export default new ChatService();
