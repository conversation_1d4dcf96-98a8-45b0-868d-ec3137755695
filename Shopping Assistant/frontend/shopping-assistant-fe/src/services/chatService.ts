// Version: 1.0.2 - Import types from separate file
import api from "./api";
import type {
  Message,
  ChatRequest,
  ChatResponse,
  ChatHistory,
  MessageResponse,
} from "../types/chat";

class ChatService {
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await api.post("/chat", request);
    return response.data;
  }

  async getChatHistory(chatId: string): Promise<MessageResponse[]> {
    const response = await api.get(`/chat/${chatId}/history`);
    return response.data;
  }

  async getUserChats(userId: string): Promise<ChatHistory[]> {
    const response = await api.get(`/chat/${userId}/user-chats`);
    return response.data;
  }

  async getChatDetails(chatId: string): Promise<ChatHistory> {
    const response = await api.get(`/chat/${chatId}`);
    return response.data;
  }

  async updateChatTitle(chatId: string, title: string): Promise<ChatHistory> {
    const response = await api.put(`/chat/${chatId}/title`, { title });
    return response.data;
  }

  async deleteChat(chatId: string): Promise<{ message: string }> {
    const response = await api.delete(`/chat/${chatId}`);
    return response.data;
  }

  async getChatModels(): Promise<any[]> {
    const response = await api.get("/chat/models");
    return response.data;
  }
}

export default new ChatService();
