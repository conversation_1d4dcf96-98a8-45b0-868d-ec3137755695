import api from "./api";

export interface UserSignup {
  email: string;
  username: string;
  password: string;
}

export interface UserLogin {
  email: string;
  password: string;
}

export interface AuthUserData {
  id: string;
  email: string;
  username: string;
  created_at: string;
  is_super_admin: boolean;
}

export interface LoginResponse {
  token: {
    access_token: string;
    token_type: string;
    refresh_token: string;
  };
  user_data: AuthUserData;
  user_roles: Array<{
    role: string;
    resource_id: string | null;
    resource_type: string | null;
  }>;
}

class AuthService {
  async signup(userData: UserSignup): Promise<AuthUserData> {
    const response = await api.post("/auth/signup", userData);
    return response.data;
  }

  async login(credentials: UserLogin): Promise<LoginResponse> {
    const response = await api.post("/auth/login", credentials);
    const data = response.data;

    // Store tokens and user data
    localStorage.setItem("access_token", data.token.access_token);
    localStorage.setItem("refresh_token", data.token.refresh_token);
    localStorage.setItem("user", JSON.stringify(data.user_data));

    return data;
  }

  async logout(): Promise<void> {
    try {
      await api.post("/auth/logout");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      // Clear local storage regardless of API call success
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      localStorage.removeItem("user");
    }
  }

  async getCurrentUser(): Promise<AuthUserData | null> {
    try {
      const response = await api.get("/auth/me");
      return response.data;
    } catch (error) {
      return null;
    }
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem("access_token");
  }

  getStoredUser(): AuthUserData | null {
    const userStr = localStorage.getItem("user");
    return userStr ? JSON.parse(userStr) : null;
  }
}

export default new AuthService();
