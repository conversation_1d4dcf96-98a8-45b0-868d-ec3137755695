@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";

@layer base {
  html {
    font-family: "Inter", system-ui, sans-serif;
  }

  body {
    background-color: #f9fafb;
    color: #111827;
  }
}

@layer components {
  .btn-primary {
    background-color: #2563eb;
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }

  .btn-primary:hover {
    background-color: #1d4ed8;
  }

  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px white;
  }

  .btn-secondary {
    background-color: #e5e7eb;
    color: #111827;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }

  .btn-secondary:hover {
    background-color: #d1d5db;
  }

  .btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 2px #6b7280, 0 0 0 4px white;
  }

  .input-field {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: border-color 0.2s;
  }

  .input-field:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px #3b82f6;
  }

  .card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
  }

  .chat-message {
    padding: 1rem;
    border-radius: 0.5rem;
    max-width: 80%;
    word-break: break-words;
  }

  .chat-message-user {
    background-color: #2563eb;
    color: white;
    margin-left: auto;
  }

  .chat-message-assistant {
    background-color: #f3f4f6;
    color: #111827;
  }
}
