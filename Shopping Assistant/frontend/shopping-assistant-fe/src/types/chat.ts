export interface Message {
  role: "user" | "assistant";
  content: string;
}

export interface ChatRequest {
  messages: Message[];
  chat_id?: string;
}

export interface ChatResponse {
  chat_id: string;
  response: string;
  status: string;
}

export interface ChatHistory {
  chat_id: string;
  title: string;
  created_at: string;
  updated_at: string;
}

export interface MessageResponse {
  message_id: string;
  role: string;
  content: string;
  timestamp: string;
}
