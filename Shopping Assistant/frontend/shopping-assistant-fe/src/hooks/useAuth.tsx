import React, { createContext, useContext, useState, useEffect } from "react";
import type { ReactNode } from "react";
import authService from "../services/auth";
import type { AuthUserData, UserLogin, UserSignup } from "../services/auth";

interface AuthContextType {
  user: AuthUserData | null;
  loading: boolean;
  login: (credentials: UserLogin) => Promise<void>;
  signup: (userData: UserSignup) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUserData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        if (authService.isAuthenticated()) {
          const currentUser = await authService.getCurrentUser();
          if (currentUser) {
            setUser(currentUser);
          } else {
            // Token is invalid, clear storage
            await authService.logout();
          }
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
        await authService.logout();
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: UserLogin) => {
    try {
      const response = await authService.login(credentials);
      setUser(response.user_data);
    } catch (error) {
      throw error;
    }
  };

  const signup = async (userData: UserSignup) => {
    try {
      await authService.signup(userData);
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error("Logout error:", error);
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    signup,
    logout,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
