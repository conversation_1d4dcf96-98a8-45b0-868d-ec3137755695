# Shopping Assistant Frontend

A modern, responsive React frontend for the Shopping Assistant application with authentication and chat functionality.

## Features

- 🔐 **Authentication System**

  - User registration and login
  - JWT token management with automatic refresh
  - Protected routes

- 💬 **Chat Interface**

  - Real-time chat with AI assistant
  - Chat history management
  - Message persistence
  - Responsive design

- 🎨 **Modern UI/UX**

  - Clean, modern design with Tailwind CSS
  - Smooth animations with Framer Motion
  - Responsive layout for all devices
  - Light theme with professional styling

- 🚀 **Performance**
  - Fast loading with Vite
  - Optimized bundle size
  - Efficient state management

## Tech Stack

- **React 19** - Latest React with hooks
- **TypeScript** - Type safety and better development experience
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **React Router** - Client-side routing
- **Axios** - HTTP client for API communication
- **Lucide React** - Beautiful icons

## Project Structure

```
src/
├── components/
│   ├── auth/
│   │   ├── SignIn.tsx
│   │   └── SignUp.tsx
│   ├── dashboard/
│   │   ├── Dashboard.tsx
│   │   ├── ChatMessage.tsx
│   │   └── Sidebar.tsx
│   └── common/
│       └── ProtectedRoute.tsx
├── services/
│   ├── api.ts
│   ├── authService.ts
│   └── chatService.ts
├── hooks/
│   └── useAuth.tsx
├── types/
├── utils/
└── App.tsx
```

## Getting Started

### Prerequisites

- Node.js 20 or higher
- npm or yarn

### Installation

1. Clone the repository
2. Navigate to the frontend directory:

   ```bash
   cd frontend/shopping-assistant-fe
   ```

3. Install dependencies:

   ```bash
   npm install
   ```

4. Start the development server:

   ```bash
   npm run dev
   ```

5. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## API Configuration

The frontend is configured to connect to the backend API at `http://localhost:8000`. You can modify this in `src/services/api.ts`:

```typescript
const API_BASE_URL = "http://localhost:8000";
```

## Features in Detail

### Authentication

- **Sign Up**: Create a new account with email, username, and password
- **Sign In**: Login with existing credentials
- **Auto-logout**: Automatic logout on token expiration
- **Protected Routes**: Redirect to login for unauthenticated users

### Chat Interface

- **Real-time Messaging**: Send and receive messages instantly
- **Chat History**: View and continue previous conversations
- **Chat Management**: Create, edit, and delete chat sessions
- **Responsive Design**: Works on desktop, tablet, and mobile

### UI Components

- **Modern Design**: Clean, professional interface
- **Smooth Animations**: Framer Motion powered transitions
- **Responsive Layout**: Adapts to different screen sizes
- **Accessibility**: Keyboard navigation and screen reader support

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Code Style

The project uses:

- TypeScript for type safety
- ESLint for code linting
- Prettier for code formatting
- Tailwind CSS for styling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
